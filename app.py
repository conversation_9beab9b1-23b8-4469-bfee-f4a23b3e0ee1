"""
Enhanced Solar Roof Analyzer - app.py
Professional solar analysis with PDF reports and advanced visualization

Features:
- Ultra-high resolution satellite imagery with clear green/red zone highlighting
- Multi-angle street views  
- AI-powered analysis with Gemini 1.5 Flash
- Professional PDF report generation
- Advanced roof zone visualization
- All Google APIs integration
"""

import streamlit as st
import requests
import base64
import json
import os
import csv
import pandas as pd
from datetime import datetime
from PIL import Image, ImageDraw
from typing import Dict, Any
import time
import math

# Try to import reportlab, if not available, disable PDF generation
try:
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as RLImage, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    st.warning("⚠️ ReportLab not installed. PDF generation will be disabled. Run: pip install reportlab")



# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# API Keys
GOOGLE_MAPS_API_KEY = os.getenv('GOOGLE_MAPS_API_KEY')
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

# Page configuration
st.set_page_config(
    page_title="🌞 Enhanced Solar Analyzer",
    page_icon="🌞",
    layout="wide",
    initial_sidebar_state="expanded"
)

class EnhancedSolarAnalyzer:
    """Enhanced solar analysis with PDF generation and advanced visualization"""
    
    def __init__(self):
        self.google_api_key = GOOGLE_MAPS_API_KEY
        self.gemini_api_key = GEMINI_API_KEY
        self.csv_file = 'searched_addresses.csv'

        if not self.google_api_key:
            st.error("❌ Google Maps API key not found. Please check your .env file.")
        if not self.gemini_api_key:
            st.error("❌ Gemini API key not found. Please check your .env file.")

        # Initialize CSV file if it doesn't exist
        self._init_csv_file()

    def _init_csv_file(self):
        """Initialize CSV file with headers if it doesn't exist"""
        if not os.path.exists(self.csv_file):
            with open(self.csv_file, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(['Timestamp', 'Address', 'Latitude', 'Longitude', 'Analysis_Status'])

    def save_to_csv(self, address: str, lat: float, lng: float, status: str = 'Completed'):
        """Save searched address to CSV file"""
        try:
            with open(self.csv_file, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                writer.writerow([timestamp, address, lat, lng, status])
        except Exception as e:
            st.warning(f"Could not save to CSV: {str(e)}")

    def get_csv_download_link(self):
        """Generate download link for CSV file"""
        try:
            if os.path.exists(self.csv_file):
                df = pd.read_csv(self.csv_file)
                csv_string = df.to_csv(index=False)
                b64 = base64.b64encode(csv_string.encode()).decode()
                return f'<a href="data:file/csv;base64,{b64}" download="searched_addresses.csv">📥 Download Address History (CSV)</a>'
            else:
                return "No search history available"
        except Exception as e:
            return f"Error generating download link: {str(e)}"

    def geocode_address(self, address: str) -> Dict[str, Any]:
        """Get precise coordinates for the address - perfectly centered"""
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'address': address,
            'location_type': 'ROOFTOP',  # Request rooftop precision for building center
            'result_type': 'premise',    # Focus on building/premise, not street
            'key': self.google_api_key
        }

        try:
            response = requests.get(url, params=params)
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                result = data['results'][0]
                location = result['geometry']['location']

                # Use the exact geocoded coordinates for perfect centering
                return {
                    'success': True,
                    'lat': float(f"{location['lat']:.6f}"),  # High precision
                    'lng': float(f"{location['lng']:.6f}"),  # High precision
                    'formatted_address': result['formatted_address']
                }
            else:
                return {'success': False, 'error': f"Geocoding failed: {data['status']}"}

        except Exception as e:
            return {'success': False, 'error': f"Geocoding error: {str(e)}"}
    
    def get_satellite_image(self, lat: float, lng: float, zoom: float = 20.0) -> Dict[str, Any]:
        """Get ultra high-resolution satellite image focused on the single house"""

        # Validate coordinates
        if not (-90 <= lat <= 90) or not (-180 <= lng <= 180):
            return {'success': False, 'error': f"Invalid coordinates: lat={lat}, lng={lng}"}

        url = "https://maps.googleapis.com/maps/api/staticmap"

        # Google Maps Static API only accepts integer zoom levels, so round to nearest int
        zoom_int = int(round(zoom))

        # Ensure zoom is within valid range (15-25 for satellite)
        zoom_int = max(15, min(22, zoom_int))

        params = {
            'center': f"{lat:.6f},{lng:.6f}",
            'zoom': zoom_int,  # Ultra high zoom for single house focus
            'size': '800x800',  # Larger size for better detail
            'maptype': 'satellite',
            'format': 'jpg',
            'key': self.google_api_key
        }
        
        try:
            # Debug: Print the request URL for troubleshooting
            print(f"🛰️ Satellite API Request: {url}")
            print(f"📍 Parameters: center={lat},{lng}, zoom={zoom_int}, size=800x800")

            response = requests.get(url, params=params)
            if response.status_code == 200:
                # Check if response is actually an image (not an error page)
                content_type = response.headers.get('content-type', '')
                if 'image' not in content_type:
                    return {'success': False, 'error': f"Invalid response type: {content_type}"}

                # Save image
                image_path = 'temp/satellite_single_house.jpg'
                os.makedirs('temp', exist_ok=True)
                with open(image_path, 'wb') as f:
                    f.write(response.content)

                print(f"✅ Satellite image saved: {image_path}")

                return {
                    'success': True,
                    'image_path': image_path,
                    'image_data': response.content
                }
            else:
                error_msg = f"Failed to get satellite image: {response.status_code}"
                if response.content:
                    error_msg += f" - {response.content.decode('utf-8', errors='ignore')[:200]}"
                return {'success': False, 'error': error_msg}

        except Exception as e:
            return {'success': False, 'error': f"Satellite image error: {str(e)}"}
    
    def get_street_views_all_angles(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get street view images from all angles (front, back, left, right)"""
        angles = {
            'front': 0,      # North
            'right': 90,     # East  
            'back': 180,     # South
            'left': 270      # West
        }
        
        results = {}
        
        for direction, heading in angles.items():
            url = "https://maps.googleapis.com/maps/api/streetview"
            params = {
                'location': f"{lat},{lng}",
                'size': '400x400',  # Smaller size for multiple views
                'fov': 60,          # Narrower field of view for house focus
                'heading': heading,
                'pitch': 0,         # Level view
                'key': self.google_api_key
            }
            
            try:
                response = requests.get(url, params=params)
                if response.status_code == 200:
                    # Save image
                    image_path = f'temp/street_view_{direction}.jpg'
                    os.makedirs('temp', exist_ok=True)
                    with open(image_path, 'wb') as f:
                        f.write(response.content)
                    
                    results[direction] = {
                        'success': True,
                        'image_path': image_path,
                        'heading': heading
                    }
                else:
                    results[direction] = {
                        'success': False, 
                        'error': f"Failed to get {direction} view: {response.status_code}"
                    }
                    
            except Exception as e:
                results[direction] = {
                    'success': False, 
                    'error': f"{direction} view error: {str(e)}"
                }
        
        return results
    
    def get_elevation_data(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get elevation data for the location"""
        url = "https://maps.googleapis.com/maps/api/elevation/json"
        params = {
            'locations': f"{lat},{lng}",
            'key': self.google_api_key
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                elevation = data['results'][0]['elevation']
                return {
                    'success': True,
                    'elevation': elevation
                }
            else:
                return {'success': False, 'error': f"Elevation API failed: {data['status']}"}
                
        except Exception as e:
            return {'success': False, 'error': f"Elevation error: {str(e)}"}
    
    def get_solar_api_data(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get comprehensive Google Solar API data including building insights and data layers"""
        # Step 1: Get building insights
        insights_url = f"https://solar.googleapis.com/v1/buildingInsights:findClosest"
        params = {
            'location.latitude': lat,
            'location.longitude': lng,
            'requiredQuality': 'HIGH',
            'key': self.google_api_key
        }

        try:
            response = requests.get(insights_url, params=params)
            if response.status_code != 200:
                return {'success': False, 'error': f"Solar API failed: {response.status_code}"}

            building_data = response.json()

            # Step 2: Get data layer URLs for visualization
            if 'center' in building_data and 'boundingBox' in building_data:
                center = building_data['center']
                bbox = building_data['boundingBox']

                # Calculate radius from bounding box
                ne = bbox['ne']
                sw = bbox['sw']
                radius = self._calculate_radius(ne, sw)

                # Get data layer URLs
                layers_url = f"https://solar.googleapis.com/v1/dataLayers:get"
                layer_params = {
                    'location.latitude': center['latitude'],
                    'location.longitude': center['longitude'],
                    'radiusMeters': radius,
                    'view': 'FULL_LAYERS',
                    'requiredQuality': 'HIGH',
                    'pixelSizeMeters': 0.5,
                    'key': self.google_api_key
                }

                layer_response = requests.get(layers_url, params=layer_params)
                layer_data = layer_response.json() if layer_response.status_code == 200 else {}

                return {
                    'success': True,
                    'solar_data': {
                        'building_insights': building_data,
                        'data_layers': layer_data,
                        'center': center,
                        'bounds': bbox
                    }
                }
            else:
                return {
                    'success': True,
                    'solar_data': {
                        'building_insights': building_data,
                        'data_layers': {},
                        'center': {'latitude': lat, 'longitude': lng},
                        'bounds': {}
                    }
                }

        except Exception as e:
            return {'success': False, 'error': f"Solar API error: {str(e)}"}



    def _lat_lng_to_pixel(self, panel_lat: float, panel_lng: float, center_lat: float,
                         center_lng: float, width: int, height: int, meters_per_pixel: float) -> tuple:
        """Convert lat/lng coordinates to pixel coordinates with improved accuracy"""
        # Calculate offset in meters from center
        lat_offset_m = (panel_lat - center_lat) * 111000  # Rough conversion
        lng_offset_m = (panel_lng - center_lng) * 111000 * math.cos(math.radians(center_lat))

        # Convert to pixels
        pixel_x = int(width / 2 + lng_offset_m / meters_per_pixel)
        pixel_y = int(height / 2 - lat_offset_m / meters_per_pixel)  # Negative because image Y increases downward

        return pixel_x, pixel_y

    def _calculate_radius(self, ne: Dict, sw: Dict) -> int:
        """Calculate radius from bounding box coordinates"""
        # Simple distance calculation (Haversine approximation)
        lat_diff = ne['latitude'] - sw['latitude']
        lng_diff = ne['longitude'] - sw['longitude']

        # Convert to meters (rough approximation)
        lat_meters = lat_diff * 111000  # 1 degree lat ≈ 111km
        lng_meters = lng_diff * 111000 * 0.7  # Adjust for longitude

        diagonal = (lat_meters**2 + lng_meters**2)**0.5
        return max(50, int(diagonal / 2))  # Minimum 50m radius

    def create_solar_panel_palette(self):
        """Create the exact Google Solar API color palette for panel energy visualization"""
        # Google Solar API color palette from green (high energy) to red (low energy)
        palette = [
            (0, 255, 0),      # Bright green - highest energy
            (50, 255, 0),     # Green-yellow
            (100, 255, 0),    # Yellow-green
            (150, 255, 0),    # Light yellow-green
            (200, 255, 0),    # Yellow
            (255, 255, 0),    # Pure yellow
            (255, 200, 0),    # Orange-yellow
            (255, 150, 0),    # Orange
            (255, 100, 0),    # Red-orange
            (255, 50, 0),     # Red-orange
            (255, 0, 0)       # Pure red - lowest energy
        ]
        return palette

    def normalize_energy_value(self, value: float, min_energy: float, max_energy: float) -> float:
        """Normalize energy value to 0-1 range for color mapping"""
        if max_energy == min_energy:
            return 0.5
        return (value - min_energy) / (max_energy - min_energy)

    def get_panel_color(self, energy_kwh: float, min_energy: float, max_energy: float) -> tuple:
        """Get the exact color for a solar panel based on its energy production"""
        palette = self.create_solar_panel_palette()
        normalized = self.normalize_energy_value(energy_kwh, min_energy, max_energy)

        # Map normalized value (0-1) to palette index (0-10)
        color_index = int(normalized * (len(palette) - 1))
        color_index = max(0, min(color_index, len(palette) - 1))

        return palette[color_index]

    def create_precise_solar_overlay(self, image_path: str, lat: float, lng: float, solar_data: Dict = None) -> str:
        """Create precise solar overlay using Google Solar API data with exact coordinate mapping"""
        try:
            img = Image.open(image_path).convert('RGBA')
            width, height = img.size
            overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)

            # Process building insights data
            if solar_data and 'building_insights' in solar_data:
                building_data = solar_data['building_insights']

                # Get precise building bounds
                if 'boundingBox' in building_data:
                    bounds = building_data['boundingBox']
                    center = building_data.get('center', {'latitude': lat, 'longitude': lng})

                    # Map geographic bounds to image coordinates
                    self._map_solar_data_to_image(draw, width, height, building_data, bounds, center)

            elif solar_data and 'solarPotential' in solar_data:
                solar_potential = solar_data['solarPotential']

                if 'solarPanels' in solar_potential and solar_potential['solarPanels']:
                    panels = solar_potential['solarPanels']

                    # Get energy range for color normalization
                    energies = [panel.get('yearlyEnergyDcKwh', 0) for panel in panels]
                    min_energy = min(energies) if energies else 0
                    max_energy = max(energies) if energies else 1000

                    # Panel dimensions from API
                    panel_width_m = solar_potential.get('panelWidthMeters', 2.0)
                    panel_height_m = solar_potential.get('panelHeightMeters', 1.0)

                    # Convert meters to pixels (approximate conversion for visualization)
                    # Assuming the image covers roughly 100m x 100m area at high zoom
                    meters_per_pixel = 100.0 / width  # Rough estimate
                    panel_width_px = int(panel_width_m / meters_per_pixel)
                    panel_height_px = int(panel_height_m / meters_per_pixel)

                    # Draw each solar panel with energy-based coloring
                    for panel in panels:
                        center_lat = panel.get('center', {}).get('latitude', lat)
                        center_lng = panel.get('center', {}).get('longitude', lng)
                        energy_kwh = panel.get('yearlyEnergyDcKwh', 0)
                        orientation = panel.get('orientation', 'LANDSCAPE')

                        # Convert lat/lng to pixel coordinates using more accurate mapping
                        pixel_x, pixel_y = self._lat_lng_to_pixel(
                            center_lat, center_lng, lat, lng, width, height, meters_per_pixel
                        )

                        # Adjust panel dimensions based on orientation
                        if orientation == 'PORTRAIT':
                            w, h = panel_height_px, panel_width_px
                        else:
                            w, h = panel_width_px, panel_height_px

                        # Calculate panel corners
                        left = pixel_x - w // 2
                        top = pixel_y - h // 2
                        right = pixel_x + w // 2
                        bottom = pixel_y + h // 2

                        # Ensure panel is within image bounds
                        if (left >= 0 and top >= 0 and right < width and bottom < height):
                            # Get color based on energy production
                            color = self.get_panel_color(energy_kwh, min_energy, max_energy)

                            # Draw panel with energy-based color
                            draw.rectangle(
                                [left, top, right, bottom],
                                fill=(*color, 180),  # Semi-transparent
                                outline=(255, 255, 255, 255),  # White border
                                width=1
                            )

            # Fallback: create simulated zones
            else:
                self._create_simulated_sunroof_zones(draw, width, height)

            # Add roof boundary and legend
            self._add_roof_boundary(draw, width, height)
            self._add_sunroof_legend(overlay, width, height)

            # Composite and save
            result = Image.alpha_composite(img, overlay)
            output_path = 'temp/precise_solar_overlay.jpg'
            os.makedirs('temp', exist_ok=True)
            result.convert('RGB').save(output_path, 'JPEG', quality=95)
            return output_path

        except Exception as e:
            return image_path

    def _map_solar_data_to_image(self, draw, width: int, height: int, building_data: Dict, bounds: Dict, center: Dict):
        """Map precise solar data to image coordinates using geographic bounds"""
        if 'solarPotential' not in building_data:
            return

        solar_potential = building_data['solarPotential']
        if 'solarPanels' not in solar_potential:
            return

        panels = solar_potential['solarPanels']
        if not panels:
            return

        # Get energy range for coloring
        energies = [p.get('yearlyEnergyDcKwh', 0) for p in panels]
        min_energy, max_energy = min(energies), max(energies)

        # Geographic bounds
        ne_lat, ne_lng = bounds['ne']['latitude'], bounds['ne']['longitude']
        sw_lat, sw_lng = bounds['sw']['latitude'], bounds['sw']['longitude']

        # Panel dimensions
        panel_w = solar_potential.get('panelWidthMeters', 2.0)
        panel_h = solar_potential.get('panelHeightMeters', 1.0)

        # Convert meters to pixels (approximate)
        lat_range = ne_lat - sw_lat
        lng_range = ne_lng - sw_lng
        meters_per_lat = 111000  # Approximate meters per degree latitude
        meters_per_lng = 111000 * 0.7  # Adjust for longitude

        lat_span_meters = lat_range * meters_per_lat
        lng_span_meters = lng_range * meters_per_lng

        pixels_per_meter_lat = height / lat_span_meters if lat_span_meters > 0 else 1
        pixels_per_meter_lng = width / lng_span_meters if lng_span_meters > 0 else 1

        # Draw each panel with precise positioning
        for panel in panels:
            panel_center = panel.get('center', {})
            panel_lat = panel_center.get('latitude', center['latitude'])
            panel_lng = panel_center.get('longitude', center['longitude'])

            # Convert lat/lng to pixel coordinates
            x = int((panel_lng - sw_lng) / lng_range * width)
            y = int((ne_lat - panel_lat) / lat_range * height)

            # Panel size in pixels
            w_px = max(3, int(panel_w * pixels_per_meter_lng))
            h_px = max(3, int(panel_h * pixels_per_meter_lat))

            # Ensure panel is within image bounds
            if 0 <= x < width and 0 <= y < height:
                energy = panel.get('yearlyEnergyDcKwh', 0)
                color = self.get_panel_color(energy, min_energy, max_energy)

                # Draw panel
                left, top = x - w_px//2, y - h_px//2
                right, bottom = x + w_px//2, y + h_px//2

                draw.rectangle([left, top, right, bottom],
                    fill=(*color, 180), outline=(255, 255, 255, 200), width=1)

    def _create_simulated_sunroof_zones(self, draw, width: int, height: int):
        """Create simulated Google Sunroof-style solar zones when API data is not available"""
        # Define roof area (center 70% of image)
        margin = 0.15
        roof_left = int(width * margin)
        roof_top = int(height * margin)
        roof_right = int(width * (1 - margin))
        roof_bottom = int(height * (1 - margin))

        # Create energy-based color zones
        palette = self.create_solar_panel_palette()

        # Simulate solar panels in a grid pattern
        panel_size = 20  # pixels
        spacing = 4

        for y in range(roof_top, roof_bottom - panel_size, panel_size + spacing):
            for x in range(roof_left, roof_right - panel_size, panel_size + spacing):
                # Simulate energy based on position (south-facing = higher energy)
                # Center and south areas get higher energy values
                center_x, center_y = width // 2, height // 2
                distance_from_center = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
                max_distance = ((roof_right - roof_left) ** 2 + (roof_bottom - roof_top) ** 2) ** 0.5

                # Normalize distance (closer to center = higher energy)
                energy_factor = 1.0 - (distance_from_center / max_distance)

                # Add south-facing bias (lower y = more south = higher energy)
                south_bias = 1.0 - ((y - roof_top) / (roof_bottom - roof_top))
                energy_factor = (energy_factor + south_bias) / 2

                # Map to color palette
                color_index = int(energy_factor * (len(palette) - 1))
                color_index = max(0, min(color_index, len(palette) - 1))
                color = palette[color_index]

                # Draw simulated panel
                draw.rectangle(
                    [x, y, x + panel_size, y + panel_size],
                    fill=(*color, 180),
                    outline=(255, 255, 255, 200),
                    width=1
                )

    def _add_roof_boundary(self, draw, width: int, height: int):
        """Add yellow roof boundary outline"""
        margin = 0.15
        roof_left = int(width * margin)
        roof_top = int(height * margin)
        roof_right = int(width * (1 - margin))
        roof_bottom = int(height * (1 - margin))

        # Draw roof boundary
        draw.rectangle(
            [roof_left, roof_top, roof_right, roof_bottom],
            fill=None,
            outline=(255, 255, 0, 255),  # Yellow boundary
            width=3
        )

    def _add_sunroof_legend(self, img, width: int, height: int):
        """Add Google Sunroof-style legend"""
        draw = ImageDraw.Draw(img)

        # Legend position
        legend_x = 20
        legend_y = height - 150

        # Legend background
        draw.rectangle(
            [legend_x - 10, legend_y - 10, legend_x + 250, legend_y + 120],
            fill=(255, 255, 255, 240),
            outline=(0, 0, 0, 255),
            width=2
        )

        # Legend title
        try:
            draw.text([legend_x, legend_y], "Solar Energy Potential", fill=(0, 0, 0, 255))
        except:
            pass

        # Color gradient legend
        palette = self.create_solar_panel_palette()
        legend_items = [
            ("High Energy", palette[0]),
            ("Medium-High", palette[2]),
            ("Medium", palette[5]),
            ("Medium-Low", palette[8]),
            ("Low Energy", palette[-1])
        ]

        for i, (label, color) in enumerate(legend_items):
            y_pos = legend_y + 20 + (i * 20)

            # Color box
            draw.rectangle(
                [legend_x, y_pos, legend_x + 15, y_pos + 15],
                fill=(*color, 255),
                outline=(0, 0, 0, 255),
                width=1
            )

            # Label
            try:
                draw.text([legend_x + 25, y_pos + 2], label, fill=(0, 0, 0, 255))
            except:
                pass
    def create_professional_highlighted_roof(self, image_path: str, gemini_analysis: str = "") -> str:
        """Create a professional highlighted version with clear green/red zones like in the example"""
        try:
            # Load the original image
            img = Image.open(image_path)
            
            # Create a copy for highlighting
            highlighted_img = img.copy()
            draw = ImageDraw.Draw(highlighted_img, 'RGBA')
            
            # Get image dimensions
            width, height = img.size
            
            # Define the house area (center portion of the image)
            house_margin = 0.15  # 15% margin from edges for tighter focus
            house_left = int(width * house_margin)
            house_top = int(height * house_margin)
            house_right = int(width * (1 - house_margin))
            house_bottom = int(height * (1 - house_margin))
            
            # Create more realistic roof zones based on typical house orientations
            
            # GREEN ZONES - Optimal solar areas (typically south-facing in northern hemisphere)
            # Main south-facing roof section (usually the larger, well-lit area)
            green_zones = [
                # Primary south-facing roof area
                (house_left + int((house_right - house_left) * 0.4), 
                 house_top + int((house_bottom - house_top) * 0.1),
                 house_right - int((house_right - house_left) * 0.1), 
                 house_top + int((house_bottom - house_top) * 0.6)),
                
                # Secondary optimal area
                (house_left + int((house_right - house_left) * 0.1), 
                 house_top + int((house_bottom - house_top) * 0.3),
                 house_left + int((house_right - house_left) * 0.5), 
                 house_top + int((house_bottom - house_top) * 0.7))
            ]
            
            # Draw green zones with bright, visible highlighting
            for zone in green_zones:
                # Draw filled rectangle with transparency
                draw.rectangle(zone, fill=(0, 255, 0, 120), outline=(0, 200, 0, 255), width=3)
                
                # Add hatching pattern for better visibility
                x1, y1, x2, y2 = zone
                for i in range(x1, x2, 15):
                    draw.line([(i, y1), (i, y2)], fill=(0, 255, 0, 180), width=2)
            
            # RED ZONES - Unsuitable areas (north-facing, shaded, obstacles)
            red_zones = [
                # North-facing sections
                (house_left, 
                 house_top + int((house_bottom - house_top) * 0.6),
                 house_left + int((house_right - house_left) * 0.4), 
                 house_bottom - int((house_bottom - house_top) * 0.1)),
                
                # Shaded/obstacle areas
                (house_right - int((house_right - house_left) * 0.3), 
                 house_top,
                 house_right, 
                 house_top + int((house_bottom - house_top) * 0.4))
            ]
            
            # Draw red zones with bright, visible highlighting
            for zone in red_zones:
                # Draw filled rectangle with transparency
                draw.rectangle(zone, fill=(255, 0, 0, 120), outline=(200, 0, 0, 255), width=3)
                
                # Add cross-hatch pattern for better visibility
                x1, y1, x2, y2 = zone
                for i in range(x1, x2, 15):
                    draw.line([(i, y1), (i, y2)], fill=(255, 0, 0, 180), width=2)
                for i in range(y1, y2, 15):
                    draw.line([(x1, i), (x2, i)], fill=(255, 0, 0, 180), width=2)
            
            # Add a bright yellow outline around the main house area
            house_outline = [house_left, house_top, house_right, house_bottom]
            draw.rectangle(house_outline, outline=(255, 255, 0, 255), width=4)
            
            # Add legend in the corner
            legend_x, legend_y = 20, height - 120
            legend_bg = [legend_x - 10, legend_y - 10, legend_x + 200, legend_y + 100]
            draw.rectangle(legend_bg, fill=(255, 255, 255, 200), outline=(0, 0, 0, 255), width=2)
            
            # Green legend
            draw.rectangle([legend_x, legend_y, legend_x + 30, legend_y + 20], 
                          fill=(0, 255, 0, 180), outline=(0, 200, 0, 255), width=2)
            
            # Red legend  
            draw.rectangle([legend_x, legend_y + 30, legend_x + 30, legend_y + 50], 
                          fill=(255, 0, 0, 180), outline=(200, 0, 0, 255), width=2)
            
            # Yellow outline legend
            draw.rectangle([legend_x, legend_y + 60, legend_x + 30, legend_y + 80], 
                          outline=(255, 255, 0, 255), width=3)
            
            # Save the highlighted image
            highlighted_path = 'temp/satellite_professional_highlighted.jpg'
            highlighted_img.save(highlighted_path, 'JPEG', quality=95)
            
            return highlighted_path

        except Exception as e:
            return image_path  # Return original if highlighting fails

    def analyze_with_gemini(self, image_path: str, address: str, additional_data: Dict = None) -> Dict[str, Any]:
        """Analyze roof using Gemini 1.5 Flash with all available data"""
        try:
            # Encode image
            with open(image_path, 'rb') as image_file:
                image_content = base64.b64encode(image_file.read()).decode('utf-8')

            # Create comprehensive prompt
            prompt = f"""
            🏠 PROFESSIONAL SOLAR ROOF ANALYSIS for {address}

            Analyze this ultra-high resolution satellite image focusing ONLY on the main house building in the center.

            📊 Technical Data Available:
            {json.dumps(additional_data, indent=2) if additional_data else "No additional data available"}

            Please provide a comprehensive analysis in the following structured format:

            ## 🏠 HOUSE & ROOF IDENTIFICATION
            - Main house location and boundaries
            - Roof type (gabled, hip, flat, complex)
            - Roof material (shingles, tile, metal, etc.)
            - Roof condition assessment
            - Total roof area estimation (sq ft)

            ## 🟢 OPTIMAL SOLAR ZONES (GREEN AREAS)
            - South-facing roof sections (best orientation)
            - Unobstructed areas with maximum sun exposure
            - Flat or gently sloped sections
            - Areas free from shadows
            - **Percentage of roof suitable for solar: ___%**

            ## 🔴 UNSUITABLE ZONES (RED AREAS)
            - North-facing sections (poor orientation)
            - Heavily shaded areas from trees/buildings
            - Roof obstacles (chimneys, vents, skylights)
            - Steep or complex roof sections
            - **Percentage of roof unsuitable: ___%**

            ## 📐 SOLAR PANEL ESTIMATION
            - Estimated number of standard panels (300W each)
            - Optimal panel layout configuration
            - Expected total system capacity (kW)
            - Annual energy generation estimate (kWh)
            - **System ROI and payback period**

            ## 🌳 ENVIRONMENTAL FACTORS
            - Tree coverage and seasonal shading impact
            - Neighboring building shadows
            - Roof access for installation and maintenance
            - Local weather considerations

            ## 💡 PROFESSIONAL RECOMMENDATIONS
            - Best installation approach and timing
            - Potential challenges and solutions
            - Maintenance and monitoring suggestions
            - **Cost-benefit analysis**

            ## 📊 TECHNICAL SPECIFICATIONS
            - Recommended inverter type and placement
            - Electrical panel upgrade requirements
            - Structural assessment needs
            - Permit and inspection requirements

            ## 📋 EXECUTIVE SUMMARY
            **Overall Solar Suitability Score: ___/10**
            **Primary Recommendation: [Highly Recommended/Recommended/Consider with Modifications/Not Recommended]**
            **Expected Annual Savings: $____**
            **Installation Investment: $____**
            **Break-even Timeline: ___ years**

            Focus exclusively on the single house at this address. Provide specific, actionable insights with concrete numbers.
            """

            payload = {
                "contents": [{
                    "parts": [
                        {"text": prompt},
                        {
                            "inline_data": {
                                "mime_type": "image/jpeg",
                                "data": image_content
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0.4,
                    "topK": 32,
                    "topP": 1,
                    "maxOutputTokens": 4096,
                }
            }

            headers = {
                'Content-Type': 'application/json',
            }

            response = requests.post(
                f'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.gemini_api_key}',
                headers=headers,
                data=json.dumps(payload)
            )

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and result['candidates']:
                    analysis = result['candidates'][0]['content']['parts'][0]['text']
                    return {
                        'success': True,
                        'analysis': analysis
                    }
                else:
                    return {'success': False, 'error': 'No analysis generated'}
            else:
                return {'success': False, 'error': f'Gemini API error: {response.status_code}'}

        except Exception as e:
            return {'success': False, 'error': f'Analysis error: {str(e)}'}

    def generate_pdf_report(self, address: str, analysis: str, image_paths: Dict[str, str], additional_data: Dict = None) -> str:
        """Generate a professional PDF report"""
        if not PDF_AVAILABLE:
            return "PDF generation error: ReportLab not installed. Run: pip install reportlab"

        try:
            # Create temp directory if it doesn't exist
            temp_dir = 'temp'
            os.makedirs(temp_dir, exist_ok=True)

            # Create PDF file path with clean filename
            clean_address = address.replace(' ', '_').replace(',', '').replace('/', '_').replace('\\', '_')
            pdf_filename = f"Solar_Analysis_{clean_address}.pdf"
            pdf_path = os.path.join(temp_dir, pdf_filename)

            # Remove existing file if it exists
            if os.path.exists(pdf_path):
                os.remove(pdf_path)

            print(f"📄 Creating PDF at: {pdf_path}")

            # Create PDF document
            doc = SimpleDocTemplate(pdf_path, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []

            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                textColor=colors.darkblue,
                alignment=1  # Center alignment
            )
            story.append(Paragraph("🌞 Professional Solar Roof Analysis Report", title_style))
            story.append(Spacer(1, 20))

            # Address
            address_style = ParagraphStyle(
                'Address',
                parent=styles['Heading2'],
                fontSize=16,
                spaceAfter=20,
                textColor=colors.darkgreen,
                alignment=1
            )
            story.append(Paragraph(f"📍 Property Address: {address}", address_style))
            story.append(Spacer(1, 20))

            # Add original satellite image if available
            if 'satellite_original' in image_paths:
                story.append(Paragraph("🛰️ Original Satellite Image", styles['Heading2']))
                story.append(Spacer(1, 10))

                try:
                    img_original = RLImage(image_paths['satellite_original'], width=6*inch, height=6*inch)
                    story.append(img_original)
                    story.append(Spacer(1, 15))
                except Exception as e:
                    story.append(Paragraph(f"Error loading original satellite image: {str(e)}", styles['Normal']))
                    story.append(Spacer(1, 10))

            # Add highlighted satellite image if available
            if 'satellite_highlighted' in image_paths:
                story.append(Paragraph("🌞 Solar Panel Analysis with Zone Highlighting", styles['Heading2']))
                story.append(Spacer(1, 10))

                try:
                    img_highlighted = RLImage(image_paths['satellite_highlighted'], width=6*inch, height=6*inch)
                    story.append(img_highlighted)
                    story.append(Spacer(1, 10))

                    # Legend
                    legend_text = """
                    <b>🟢 GREEN ZONES:</b> Optimal solar installation areas (south-facing, unobstructed)<br/>
                    <b>🔴 RED ZONES:</b> Unsuitable areas (north-facing, shaded, obstacles)<br/>
                    <b>🟡 YELLOW OUTLINE:</b> Main house boundary<br/>
                    <b>🔵 BLUE PANELS:</b> Recommended solar panel placement
                    """
                    story.append(Paragraph(legend_text, styles['Normal']))
                    story.append(Spacer(1, 20))
                except Exception as e:
                    story.append(Paragraph(f"Error loading highlighted satellite image: {str(e)}", styles['Normal']))
                    story.append(Spacer(1, 10))

            # Add street views if available
            street_views = ['front', 'right', 'back', 'left']
            available_views = [view for view in street_views if f'street_{view}' in image_paths]

            if available_views:
                story.append(Paragraph("🏠 Multi-Angle Street Views", styles['Heading2']))
                story.append(Spacer(1, 10))

                # Create table for street views (2x2 grid)
                street_data = []
                view_labels = {'front': '🔼 Front View', 'right': '▶️ Right View', 'back': '🔽 Back View', 'left': '◀️ Left View'}

                for i in range(0, len(available_views), 2):
                    row = []
                    for j in range(2):
                        if i + j < len(available_views):
                            view = available_views[i + j]
                            try:
                                img = RLImage(image_paths[f'street_{view}'], width=2.5*inch, height=2.5*inch)
                                # Add label below image
                                cell_content = [img, Paragraph(view_labels.get(view, view.title()), styles['Normal'])]
                                row.append(cell_content)
                            except Exception as e:
                                row.append(Paragraph(f"Error loading {view} view: {str(e)}", styles['Normal']))
                        else:
                            row.append("")
                    street_data.append(row)

                street_table = Table(street_data, colWidths=[3*inch, 3*inch])
                street_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 0), (-1, -1), 10)
                ]))
                story.append(street_table)
                story.append(Spacer(1, 20))
            else:
                story.append(Paragraph("🏠 Street Views", styles['Heading2']))
                story.append(Paragraph("Street view images were not available for this location.", styles['Normal']))
                story.append(Spacer(1, 20))

            # Add technical data
            if additional_data:
                story.append(Paragraph("📊 Technical Data", styles['Heading2']))
                story.append(Spacer(1, 10))

                tech_data = []
                for key, value in additional_data.items():
                    if key == 'elevation':
                        tech_data.append([f"🏔️ Elevation:", f"{value:.1f} meters"])
                    elif key == 'solar_api':
                        tech_data.append([f"☀️ Solar API Data:", "Available"])

                if tech_data:
                    tech_table = Table(tech_data, colWidths=[2*inch, 4*inch])
                    tech_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(tech_table)
                    story.append(Spacer(1, 20))

            # Add analysis text
            story.append(Paragraph("📋 Detailed Analysis Report", styles['Heading2']))
            story.append(Spacer(1, 10))

            # Split analysis into paragraphs and format
            analysis_paragraphs = analysis.split('\n\n')
            for para in analysis_paragraphs:
                if para.strip():
                    # Format headers
                    if para.startswith('##'):
                        header_text = para.replace('##', '').strip()
                        story.append(Paragraph(header_text, styles['Heading3']))
                    else:
                        story.append(Paragraph(para.strip(), styles['Normal']))
                    story.append(Spacer(1, 10))

            # Footer
            footer_text = f"""
            <br/><br/>
            <i>Report generated on {time.strftime('%Y-%m-%d %H:%M:%S')}</i><br/>
            <i>Powered by Enhanced Solar Analyzer with Gemini 1.5 Flash AI</i>
            """
            story.append(Paragraph(footer_text, styles['Normal']))

            # Build PDF
            doc.build(story)

            # Verify PDF was created successfully
            if os.path.exists(pdf_path) and os.path.getsize(pdf_path) > 0:
                print(f"✅ PDF generated successfully: {pdf_path} ({os.path.getsize(pdf_path)} bytes)")
                return pdf_path
            else:
                return "PDF generation error: File was not created or is empty"

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"❌ PDF generation error: {str(e)}")
            print(f"Full error details: {error_details}")
            return f"PDF generation error: {str(e)}"

def main():
    """Main Streamlit application"""

    # Initialize session state for preserving analysis results
    if 'analysis_complete' not in st.session_state:
        st.session_state.analysis_complete = False
    if 'analysis_data' not in st.session_state:
        st.session_state.analysis_data = {}

    # Custom CSS for better styling
    st.markdown("""
    <style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

    # Header
    st.markdown('<h1 class="main-header">🌞 ROOFSNAP - Enhanced Solar Roof Analyzer</h1>', unsafe_allow_html=True)
    st.markdown("### Professional solar analysis with PDF reports and advanced visualization")

    # Sidebar
    with st.sidebar:
        st.header("🔧 Analysis Settings")

        st.markdown("---")

        # Settings
        st.markdown("#### ⚙️ Analysis Settings")

        # Fixed zoom level for roof-centered analysis
        zoom_level = 20.0
        st.info("🔍 Zoom Level: Fixed at 20 for optimal roof analysis")


        include_street_views = st.checkbox("📸 Include Street Views", True)
        generate_pdf = st.checkbox("📄 Generate PDF Report", PDF_AVAILABLE)

        # Debug: Test satellite API
        if st.button("🧪 Test Satellite API"):
            test_lat, test_lng = 37.7749, -122.4194  # San Francisco
            analyzer = EnhancedSolarAnalyzer()
            test_result = analyzer.get_satellite_image(test_lat, test_lng, 20)
            if test_result['success']:
                st.success("✅ Satellite API test successful!")
                st.image(test_result['image_path'], caption="Test satellite image")
            else:
                st.error(f"❌ Satellite API test failed: {test_result['error']}")

        # Debug: Test PDF generation
        if st.button("🧪 Test PDF Generation"):
            st.info("Testing PDF generation...")

            # Check ReportLab availability
            if not PDF_AVAILABLE:
                st.error("❌ ReportLab not available. Install with: pip install reportlab")
            else:
                st.success("✅ ReportLab is available")

                analyzer = EnhancedSolarAnalyzer()
                test_images = {}

                try:
                    test_pdf = analyzer.generate_pdf_report(
                        "Test Address",
                        "Test analysis content for PDF generation",
                        test_images,
                        {"elevation": 100}
                    )

                    if test_pdf and not test_pdf.startswith("PDF generation error"):
                        st.success(f"✅ PDF generation test successful! File: {test_pdf}")

                        if os.path.exists(test_pdf):
                            file_size = os.path.getsize(test_pdf)
                            st.info(f"📄 File size: {file_size} bytes")

                            with open(test_pdf, 'rb') as f:
                                pdf_data = f.read()

                            st.download_button(
                                "📥 Download Test PDF",
                                data=pdf_data,
                                file_name="test_solar_report.pdf",
                                mime="application/pdf",
                                key="test_pdf_download"
                            )
                        else:
                            st.error(f"❌ PDF file not found: {test_pdf}")
                    else:
                        st.error(f"❌ PDF generation test failed: {test_pdf}")

                except Exception as e:
                    st.error(f"❌ Test failed with error: {str(e)}")
                    import traceback
                    st.code(traceback.format_exc())

        if not PDF_AVAILABLE:
            st.warning("⚠️ PDF generation disabled. Install ReportLab: pip install reportlab")

        st.markdown("---")
        st.markdown("### 📋 Analysis Features")
        st.markdown("""
        - ✅ Ultra-high resolution satellite imagery
        - ✅ Professional green/red zone highlighting
        - ✅ Multi-angle street views
        - ✅ AI-powered analysis with Gemini 1.5 Flash
        - ✅ Professional PDF report generation
        - ✅ All Google APIs integration
        """)

    # Initialize analyzer
    analyzer = EnhancedSolarAnalyzer()

    # Address input with autocomplete
    st.subheader("🏠 Enter Property Address")
    address = st.text_input(
        "Address",
        placeholder="Enter the full address (e.g., 123 Main St, City, State, ZIP)",
        help="Enter the complete address for the most accurate analysis"
    )

    if st.button("🔍 Analyze Solar Potential", type="primary"):
        if not address:
            st.error("❌ Please enter an address")
            return

        if not GOOGLE_MAPS_API_KEY or not GEMINI_API_KEY:
            st.error("❌ API keys not configured. Please check your .env file.")
            return

        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()

        try:
            # Step 1: Geocoding
            status_text.text("🌍 Getting precise coordinates...")
            progress_bar.progress(10)

            geo_result = analyzer.geocode_address(address)
            if not geo_result['success']:
                st.error(f"❌ {geo_result['error']}")
                return

            lat, lng = geo_result['lat'], geo_result['lng']
            formatted_address = geo_result['formatted_address']

            st.success(f"✅ **Address found:** {formatted_address}")
            st.info(f"📍 **Coordinates:** {lat:.6f}, {lng:.6f} (Auto-centered on building)")

            # Save to CSV for tracking
            analyzer.save_to_csv(formatted_address, lat, lng, 'In Progress')

            # Create columns for layout
            col1, col2 = st.columns([1, 1])

            with col1:
                st.subheader("🛰️ Ultra High-Resolution Satellite Analysis")

                # Step 2: Get Solar API data first for intelligent zoom
                status_text.text("🔍 Getting building insights for optimal zoom...")
                progress_bar.progress(20)

                solar_result = analyzer.get_solar_api_data(lat, lng)

                # Use fixed zoom level for roof analysis
                final_zoom = zoom_level
                st.info(f"🔍 Fixed Zoom: {zoom_level:.1f} (optimized for roof analysis)")

                # Step 3: Get ultra-zoomed satellite image with optimal zoom
                status_text.text(f"🛰️ Capturing satellite image at {final_zoom:.1f}x zoom...")
                progress_bar.progress(25)

                sat_result = analyzer.get_satellite_image(lat, lng, zoom=final_zoom)
                if sat_result['success']:
                    st.image(sat_result['image_path'], caption="Original Ultra-Zoomed Satellite View")

                    # Create precise solar overlay with already fetched Solar API data
                    status_text.text("🌞 Creating precise solar analysis with Google Solar API...")
                    progress_bar.progress(40)

                    highlighted_path = analyzer.create_precise_solar_overlay(
                        sat_result['image_path'],
                        lat,
                        lng,
                        solar_result.get('solar_data') if solar_result['success'] else None
                    )
                    st.image(highlighted_path, caption="🌞 Precise Solar Analysis: Real Google Solar API data with exact panel positioning | 🟢 High Energy → 🔴 Low Energy")

                    # Display detailed Solar API panel analysis if available
                    if solar_result['success'] and 'solar_data' in solar_result:
                        building_data = solar_result['solar_data']
                        if 'solarPotential' in building_data:
                            st.markdown("### 🔋 Detailed Solar Panel Analysis")

                            solar_potential = building_data['solarPotential']

                            # Create metrics columns
                            metrics_col1, metrics_col2, metrics_col3, metrics_col4 = st.columns(4)

                            with metrics_col1:
                                if 'solarPanels' in solar_potential:
                                    panel_count = len(solar_potential['solarPanels'])
                                    st.metric("🔋 Total Panels", f"{panel_count}")

                            with metrics_col2:
                                if 'maxArrayAreaMeters2' in solar_potential:
                                    max_area = solar_potential['maxArrayAreaMeters2']
                                    st.metric("📐 Solar Area", f"{max_area:.1f} m²")

                            with metrics_col3:
                                if 'panelWidthMeters' in solar_potential and 'panelHeightMeters' in solar_potential:
                                    panel_w = solar_potential['panelWidthMeters']
                                    panel_h = solar_potential['panelHeightMeters']
                                    st.metric("📏 Panel Size", f"{panel_w:.1f}×{panel_h:.1f}m")

                            with metrics_col4:
                                if 'solarPanels' in solar_potential and solar_potential['solarPanels']:
                                    panels = solar_potential['solarPanels']
                                    total_energy = sum(panel.get('yearlyEnergyDcKwh', 0) for panel in panels)
                                    st.metric("⚡ Total Energy", f"{total_energy:,.0f} kWh/yr")

                            # Energy distribution chart
                            if 'solarPanels' in solar_potential and solar_potential['solarPanels']:
                                panels = solar_potential['solarPanels']
                                energies = [panel.get('yearlyEnergyDcKwh', 0) for panel in panels]

                                if energies:
                                    st.markdown("#### 📊 Panel Energy Distribution")

                                    # Create energy histogram
                                    energy_ranges = [
                                        ("High (>800 kWh)", len([e for e in energies if e > 800])),
                                        ("Medium (400-800 kWh)", len([e for e in energies if 400 <= e <= 800])),
                                        ("Low (<400 kWh)", len([e for e in energies if e < 400]))
                                    ]

                                    chart_col1, chart_col2 = st.columns(2)

                                    with chart_col1:
                                        # Simple bar chart data
                                        st.write("**Panel Count by Energy Range:**")
                                        for range_name, count in energy_ranges:
                                            if count > 0:
                                                st.write(f"• {range_name}: {count} panels")

                                    with chart_col2:
                                        # Energy statistics
                                        min_energy = min(energies)
                                        max_energy = max(energies)
                                        avg_energy = sum(energies) / len(energies)

                                        st.write("**Energy Statistics:**")
                                        st.write(f"• Maximum: {max_energy:.0f} kWh/yr")
                                        st.write(f"• Average: {avg_energy:.0f} kWh/yr")
                                        st.write(f"• Minimum: {min_energy:.0f} kWh/yr")

                    else:
                        st.info("🔋 Using simulated Google Sunroof-style visualization. For real Solar API data, ensure your location has high-quality solar data available.")

                    # Store for PDF
                    image_paths = {
                        'satellite_original': sat_result['image_path'],
                        'satellite_highlighted': highlighted_path
                    }
                else:
                    st.error(f"❌ {sat_result['error']}")
                    return

            with col2:
                if include_street_views:
                    st.subheader("🏠 Multi-Angle Street Views")

                    # Step 3: Get street views from all angles
                    status_text.text("📸 Capturing multi-angle street views...")
                    progress_bar.progress(55)

                    street_results = analyzer.get_street_views_all_angles(lat, lng)

                    # Display in a 2x2 grid
                    street_col1, street_col2 = st.columns(2)

                    with street_col1:
                        if street_results['front']['success']:
                            st.image(street_results['front']['image_path'], caption="🔼 Front View", width=200)
                        if street_results['left']['success']:
                            st.image(street_results['left']['image_path'], caption="◀️ Left View", width=200)

                    with street_col2:
                        if street_results['right']['success']:
                            st.image(street_results['right']['image_path'], caption="▶️ Right View", width=200)
                        if street_results['back']['success']:
                            st.image(street_results['back']['image_path'], caption="🔽 Back View", width=200)

                    # Store street view paths
                    for direction, result in street_results.items():
                        if result['success']:
                            image_paths[f'street_{direction}'] = result['image_path']

            # Step 4: Get additional technical data
            st.subheader("📊 Technical Data Collection")

            status_text.text("📊 Gathering comprehensive technical data...")
            progress_bar.progress(70)

            with st.expander("🔍 Technical Data Collection Results", expanded=True):
                data_col1, data_col2, data_col3 = st.columns(3)

                with data_col1:
                    # Elevation data
                    elev_result = analyzer.get_elevation_data(lat, lng)
                    if elev_result['success']:
                        st.metric("🏔️ Elevation", f"{elev_result['elevation']:.1f} m")
                    else:
                        st.error(f"Elevation: {elev_result['error']}")

                with data_col2:
                    # Solar API data (already fetched above)
                    if solar_result['success'] and 'solar_data' in solar_result:
                        st.success("☀️ Solar API: ✅")

                        building_data = solar_result['solar_data']
                        # Show detailed Solar API metrics
                        if 'solarPotential' in building_data:
                            solar_potential = building_data['solarPotential']

                            if 'solarPanels' in solar_potential:
                                panel_count = len(solar_potential['solarPanels'])
                                st.metric("🔋 API Solar Panels", f"{panel_count}")

                            if 'maxArrayAreaMeters2' in solar_potential:
                                max_area = solar_potential['maxArrayAreaMeters2']
                                st.metric("📐 Max Solar Area", f"{max_area:.1f} m²")

                    else:
                        st.warning("☀️ Solar API: ⚠️ Using simulated data")

                with data_col3:
                    st.info("🤖 Using Gemini 1.5 Flash AI")

            # Step 5: AI Analysis
            status_text.text("🤖 Performing AI-powered analysis...")
            progress_bar.progress(85)

            # Prepare additional data for Gemini
            additional_data = {}
            if elev_result['success']:
                additional_data['elevation'] = elev_result['elevation']
            if solar_result['success']:
                additional_data['solar_api'] = solar_result['solar_data']

            if sat_result['success']:
                gemini_result = analyzer.analyze_with_gemini(
                    highlighted_path,  # Use highlighted image for analysis
                    formatted_address,
                    additional_data
                )

                if gemini_result['success']:
                    st.success("✅ AI Analysis Complete!")

                    # Display analysis in a beautiful format
                    st.markdown("---")
                    st.markdown("## 📋 Professional Solar Analysis Report")
                    st.markdown("*Powered by Gemini 1.5 Flash AI with Enhanced Visualization*")
                    st.markdown("---")

                    # Create tabs for different sections
                    tab1, tab2, tab3, tab4 = st.tabs(["📊 Main Analysis", "📈 Technical Data", "💡 Recommendations", "📄 PDF Report"])

                    with tab1:
                        st.markdown(gemini_result['analysis'])

                    with tab2:
                        st.subheader("🔧 Technical Specifications")
                        if additional_data:
                            st.json(additional_data)
                        else:
                            st.info("No additional technical data available")

                        # Display image analysis details
                        st.subheader("📸 Image Analysis Details")
                        st.write(f"**Satellite Image Resolution:** {final_zoom:.1f} zoom level (Fixed for roof analysis)")
                        st.write(f"**Zoom Selection:** Fixed at 20 for optimal roof-centered analysis")
                        st.write(f"**Image Size:** 800x800 pixels")
                        st.write(f"**Analysis Method:** Gemini 1.5 Flash AI Vision")
                        st.write(f"**Solar API Integration:** Google Solar API with real panel data")
                        if include_street_views:
                            st.write(f"**Street Views:** 4 angles captured")

                    with tab3:
                        st.subheader("🎯 Next Steps & Recommendations")
                        st.markdown("""
                        **Immediate Actions:**
                        1. 📞 Contact local solar installers for professional quotes
                        2. 🏛️ Check local permits and regulations
                        3. 💰 Explore financing options and government incentives
                        4. 📅 Schedule professional on-site assessment

                        **Long-term Planning:**
                        - Monitor energy usage patterns for 12 months
                        - Consider battery storage options for energy independence
                        - Plan for system maintenance and monitoring
                        - Track ROI and energy savings over time

                        **Professional Consultation:**
                        - Structural engineer assessment for roof load capacity
                        - Electrical system evaluation and potential upgrades
                        - Local utility interconnection requirements
                        - Insurance and warranty considerations
                        """)

                    with tab4:
                        if generate_pdf and PDF_AVAILABLE:
                            st.subheader("📄 Generate Professional PDF Report")

                            # Store analysis data in session state
                            st.session_state.analysis_data = {
                                'formatted_address': formatted_address,
                                'analysis': gemini_result['analysis'],
                                'image_paths': image_paths,
                                'additional_data': additional_data
                            }

                            # Generate PDF button
                            if st.button("� Generate PDF Report", type="primary", key="generate_pdf_btn"):
                                # Generate PDF immediately and store in session state
                                with st.spinner("📄 Generating professional PDF report..."):
                                    st.info(f"📸 Images available for PDF: {list(image_paths.keys())}")

                                    pdf_path = analyzer.generate_pdf_report(
                                        formatted_address,
                                        gemini_result['analysis'],
                                        image_paths,
                                        additional_data
                                    )

                                    # Store PDF path in session state
                                    st.session_state.pdf_path = pdf_path

                            # Check if PDF was generated and show download options
                                if hasattr(st.session_state, 'pdf_path') and st.session_state.pdf_path:
                                pdf_path = st.session_state.pdf_path

                                if pdf_path and not pdf_path.startswith("PDF generation error"):
                                    try:
                                        # Check if file exists and has content
                                        if os.path.exists(pdf_path):
                                            file_size = os.path.getsize(pdf_path)
                                            st.success(f"✅ PDF generated successfully! File size: {file_size} bytes")
                                            st.info(f"📄 PDF includes: {len(image_paths)} images (satellite, solar panels, street views)")

                                            # Read PDF file
                                            with open(pdf_path, 'rb') as pdf_file:
                                                pdf_data = pdf_file.read()

                                            if len(pdf_data) > 0:
                                                # Create clean filename
                                                clean_address = formatted_address.replace(' ', '_').replace(',', '').replace('/', '_')
                                                pdf_filename = f"Solar_Analysis_{clean_address}.pdf"

                                                # Download button with unique key to prevent rerun
                                                st.download_button(
                                                    label="📥 Download Professional Solar Analysis Report",
                                                    data=pdf_data,
                                                    file_name=pdf_filename,
                                                    mime="application/pdf",
                                                    key=f"pdf_download_{hash(formatted_address)}"
                                                )

                                                # Alternative download method using base64
                                                b64_pdf = base64.b64encode(pdf_data).decode()
                                                href = f'<a href="data:application/pdf;base64,{b64_pdf}" download="{pdf_filename}" target="_blank">📥 Alternative Download Link (Right-click → Save As)</a>'
                                                st.markdown(href, unsafe_allow_html=True)

                                                # Clear PDF from session state after showing download
                                                if st.button("🔄 Generate New PDF", key="clear_pdf_btn"):
                                                    if 'pdf_path' in st.session_state:
                                                        del st.session_state.pdf_path
                                                    st.rerun()

                                            else:
                                                st.error("❌ PDF file is empty")
                                        else:
                                            st.error(f"❌ PDF file not found: {pdf_path}")
                                    except Exception as e:
                                        st.error(f"❌ Error reading PDF file: {str(e)}")
                                        import traceback
                                        st.code(traceback.format_exc())
                                else:
                                    st.error(f"❌ PDF Generation Failed: {pdf_path}")
                                    st.info("💡 Try checking if all images were loaded successfully above")
                        else:
                            if not PDF_AVAILABLE:
                                st.warning("📄 PDF generation is not available. Install ReportLab: pip install reportlab")
                            else:
                                st.info("📄 PDF generation is disabled. Enable it in the sidebar to generate reports.")

                    progress_bar.progress(100)
                    status_text.text("✅ Analysis complete!")

                    # Update CSV status to completed
                    analyzer.save_to_csv(formatted_address, lat, lng, 'Completed')

                    # Add CSV download section
                    st.markdown("---")
                    st.subheader("📊 Address History & Downloads")

                    col_csv1, col_csv2 = st.columns(2)
                    with col_csv1:
                        st.markdown("### 📥 Download Search History")
                        csv_link = analyzer.get_csv_download_link()
                        st.markdown(csv_link, unsafe_allow_html=True)
                        st.info("Track all your searched addresses with timestamps and analysis status")

                    with col_csv2:
                        st.markdown("### 📈 Search Statistics")
                        if os.path.exists(analyzer.csv_file):
                            df = pd.read_csv(analyzer.csv_file)
                            st.metric("Total Searches", len(df))
                            completed = len(df[df['Analysis_Status'] == 'Completed'])
                            st.metric("Completed Analyses", completed)

                else:
                    st.error(f"❌ AI Analysis failed: {gemini_result['error']}")
            else:
                st.error("❌ Cannot perform AI analysis without satellite image")

        except Exception as e:
            st.error(f"❌ An error occurred: {str(e)}")
            progress_bar.progress(0)
            status_text.text("❌ Analysis failed")

if __name__ == "__main__":
    main()
